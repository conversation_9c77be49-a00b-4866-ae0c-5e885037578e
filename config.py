"""
高性能异步期现套利机器人配置文件 - 基于WebSocket的毫秒级响应架构
"""
from dataclasses import dataclass, field
from typing import Optional, Dict, List, Any, Tuple
import os
import warnings

# 加载环境变量
try:
    from load_env import setup_environment
    setup_environment()
except ImportError:
    # 如果load_env模块不存在，继续使用系统环境变量
    pass


@dataclass
class TradingConfig:
    """交易配置类 - 异步架构优化版"""
    
    # 交易所配置
    EXCHANGE_ID: str = 'okx'
    API_KEY: str = os.getenv('API_KEY', '')
    API_SECRET: str = os.getenv('API_SECRET', '')
    API_PASSWORD: str = os.getenv('API_PASSWORD', '')
    
    # 交易模式开关 - True: 模拟盘/测试网, False: 实盘
    USE_SANDBOX: bool = True
    SANDBOX_MODE: bool = True
    
    # WebSocket 连接配置
    WS_PUBLIC_URL: str = "wss://ws.okx.com:8443/ws/v5/public"
    WS_PRIVATE_URL: str = "wss://ws.okx.com:8443/ws/v5/private"
    WS_RECONNECT_INTERVAL: int = 5  # 重连间隔（秒）
    WS_PING_INTERVAL: int = 20  # 心跳间隔（秒）
    WS_PING_TIMEOUT: int = 10   # 心跳超时（秒）
    
    # 多交易对配置 - 支持同时监控多个套利对
    TRADING_PAIRS: List[Dict[str, Any]] = field(default_factory=lambda: [
        {
            "name": "DOGE_USDT",
            "spot_id": "DOGE-USDT",
            "futures_id": "DOGE-USDT-SWAP",
            "priority": 1,  # 优先级：1=高，2=中，3=低
            "enabled": True,
            "min_notional": 5.0,
            "max_leverage": 10.0,
            "tick_size": 0.00001,
            "lot_size": 1.0
        },
        {
            "name": "BTC_USDT", 
            "spot_id": "BTC-USDT",
            "futures_id": "BTC-USDT-SWAP",
            "priority": 1,
            "enabled": True,
            "min_notional": 10.0,
            "max_leverage": 5.0,
            "tick_size": 0.01,
            "lot_size": 0.00001
        },
        {
            "name": "ETH_USDT",
            "spot_id": "ETH-USDT", 
            "futures_id": "ETH-USDT-SWAP",
            "priority": 1,
            "enabled": True,
            "min_notional": 10.0,
            "max_leverage": 8.0,
            "tick_size": 0.01,
            "lot_size": 0.0001
        }
    ])
    
    # 向后兼容性：保留原有单一交易对配置
    SPOT_ID: str = "DOGE-USDT"
    FUTURES_ID: str = "DOGE-USDT-SWAP"
    
    # 高频交易配置
    MIN_PRICE_UPDATE_INTERVAL_MS: int = 100  # 最小价格更新间隔（毫秒）
    MAX_ORDER_LATENCY_MS: int = 200  # 最大订单延迟容忍（毫秒）
    
    # 基差套利策略参数 (测试阶段调整)
    BOLLINGER_WINDOW: int = 15
    BOLLINGER_STD_DEV: float = 1.5        # 降低到1.5，更容易触发信号
    BASIS_ENTRY_THRESHOLD: float = 0.0005 # 降低到0.05%，更容易触发
    BASIS_PROFIT_TARGET: float = 0.0003   # 0.03%
    BASIS_STOP_LOSS: float = 0.003        # 0.3%

    # 策略优化配置 - 分层布林带系统
    STRATEGY_OPTIMIZATION: Dict[str, Any] = field(default_factory=lambda: {
        "LAYERED_BOLLINGER_BANDS": {
            # 战略层布林带（机会过滤器与市场状态识别器）
            "STRATEGIC_BB": {
                "WINDOW": 30,        # 30分钟 - 宏观价差偏离识别
                "STD_DEV": 2.0,      # 标准宽度 - 统计显著性阈值
                "DATA_SOURCE": "minute_vwap"  # 分钟级VWAP聚合数据
            },
            # 战术层布林带（精准时机选择器）
            "TACTICAL_BB": {
                "WINDOW": 60,        # 60秒 - 短期力竭点捕捉
                "STD_DEV": 2.5,      # 更宽通道 - 要求更极端偏离
                "DATA_SOURCE": "second_vwap"  # 秒级VWAP聚合数据
            },
            # 分层信号流程控制
            "SIGNAL_FLOW": {
                "ENABLE_STRATEGIC_FILTER": True,   # 启用战略层预警
                "ENABLE_TACTICAL_CONFIRMATION": True,  # 启用战术层确认
                "FALLBACK_TO_LEGACY": True,        # 数据不足时回退到传统信号
                "MIN_STRATEGIC_DATA_POINTS": 30,   # 战略层最小数据点要求
                "MIN_TACTICAL_DATA_POINTS": 60     # 战术层最小数据点要求
            }
        }
    })

    # 数据预处理配置 - 标准化输入层
    DATA_PREPROCESSING: Dict[str, Any] = field(default_factory=lambda: {
        # VWAP聚合配置
        "VWAP_AGGREGATION": {
            # 秒级聚合（战术层使用）
            "SECOND_WINDOW_MS": 1000,       # 1秒聚合窗口
            "SECOND_MIN_TICKS": 1,          # 最小tick数要求

            # 分钟级聚合（战略层使用）
            "MINUTE_WINDOW_MS": 60000,      # 1分钟聚合窗口
            "MINUTE_MIN_TICKS": 5,          # 最小tick数要求

            # 聚合方法
            "AGGREGATION_METHOD": "vwap",   # vwap | simple_avg | median
            "VOLUME_WEIGHTED": True,        # 是否使用成交量加权
        },

        # 数据质量控制
        "QUALITY_CONTROL": {
            "MAX_PRICE_DEVIATION_PERCENT": 5.0,    # 最大价格偏离百分比（异常值过滤）
            "MIN_VOLUME_THRESHOLD": 100.0,         # 最小成交量阈值（USDT）
            "ENABLE_OUTLIER_DETECTION": True,      # 启用异常值检测
            "OUTLIER_Z_SCORE_THRESHOLD": 3.0,      # Z-score异常值阈值
        },

        # 数据平滑配置
        "SMOOTHING": {
            "ENABLE_PRICE_SMOOTHING": True,        # 启用价格平滑
            "SMOOTHING_METHOD": "ema",             # ema | sma | none
            "SMOOTHING_ALPHA": 0.3,                # EMA平滑系数
            "SMOOTHING_WINDOW": 3,                 # 平滑窗口大小
        },

        # 缓存配置
        "CACHING": {
            "MAX_CACHE_SIZE": 1000,                # 最大缓存条目数
            "CACHE_TTL_SECONDS": 300,              # 缓存生存时间（5分钟）
            "ENABLE_PERSISTENT_CACHE": False,      # 是否启用持久化缓存
        }
    })
    
    # 交易成本配置 - 净利润决策核心参数
    TRADING_COSTS: Dict[str, float] = field(default_factory=lambda: {
        # 交易手续费率
        "TAKER_FEE_RATE": 0.001,  # 0.1% - 吃单费率
        "MAKER_FEE_RATE": 0.0008,  # 0.08% - 挂单费率（优惠）

        # 滑点成本率
        "ESTIMATED_SLIPPAGE_RATE": 0.0005,  # 0.05% - 单次交易预估滑点

        # 利润安全边际 - 生产级校准
        "PROFIT_SAFETY_MARGIN": 1.5,  # 1.5倍 - 50%缓冲垫，吸收中轨漂移风险

        # 资金费率计算参数
        "FUNDING_RATE_ANNUALIZED_MULTIPLIER": 365.0,  # 年化倍数（每日结算）
        "FUNDING_SETTLEMENT_PERIODS_PER_DAY": 3,  # 每日资金费率结算次数

        # 持仓周期估算（用于资金费率影响计算）- 基于实际策略行为校准
        "EXPECTED_HOLDING_HOURS": 0.25,  # 15分钟 - 基差套利的真实平均持仓时长

        # 额外交易成本
        "MARKET_IMPACT_RATE": 0.0002,  # 0.02% - 市场冲击成本
        "PRICE_IMPROVEMENT_BUFFER": 0.0001,  # 0.01% - 价格改善缓冲
    })
    
    # 风险管理参数 - 多交易对增强版
    RISK_MANAGEMENT: Dict[str, Any] = field(default_factory=lambda: {
        # 多交易对风险控制
        "MAX_CONCURRENT_PAIRS": 3,  # 最大同时交易的交易对数
        "MAX_POSITIONS_PER_PAIR": 1,  # 每个交易对最大同时持仓数（单边策略=1）
        
        # 资金分配策略
        "TOTAL_CAPITAL_ALLOCATION": 10000,  # 增加总资金以支持多对交易
        "CAPITAL_ALLOCATION_METHOD": "priority_weighted",  # 资金分配方法
        
        # 单交易对资金限制
        "MAX_CAPITAL_PER_PAIR_RATIO": 0.4,  # 单个交易对最大资金占比40%
        "MAX_CAPITAL_PER_TRADE_RATIO": 0.15,  # 单笔交易最大资金占比15%
        
        # 爆仓安全缓冲比例
        "LIQUIDATION_SAFETY_BUFFER": 0.05,  # 5%
        
        # 最大日损失限制
        "MAX_DAILY_LOSS": -100.0,  # USDT
        
        # 止损百分比
        "STOP_LOSS_PERCENT": 0.02,  # 2%
        
        # 最小基差阈值
        "MIN_BASIS_THRESHOLD": 0.001,  # 0.1%
        
        # 资金使用效率检查
        "MIN_CAPITAL_UTILIZATION": 0.1,  # 最低10%资金使用率
        "MAX_CAPITAL_UTILIZATION": 0.9,  # 最高90%资金使用率
        
        # 仓位监控参数
        "POSITION_HEALTH_CHECK_INTERVAL": 30,  # 秒
        "MARGIN_CALL_THRESHOLD": 0.15,  # 15% 追保阈值
        
        # 紧急平仓滑点保护
        "EMERGENCY_SLIPPAGE_MULTIPLIER": 1.05,  # 紧急平仓时的滑点容忍比例 (5%)
        
        # 全局账户亏损熔断
        "MAX_TOTAL_DRAWDOWN_RATIO": 0.10,  # 整个机器人账户允许的最大回撤比例 (10%)
        
        # 流动性风险管理
        "MAX_SLIPPAGE_PERCENT_PER_TRADE": 1.0,  # 单笔交易允许的最大滑点 (1.0% - 测试环境放宽)
        
        # 分级仓位管理 - 风险定级系统
        "GRADED_POSITION_SYSTEM": {
            # 次优信号仓位缩放比例
            "SUBOPTIMAL_TRADE_SIZE_RATIO": 0.5,  # 50% - B级信号只使用标准仓位的一半
            
            # 风险过滤器控制开关 (测试阶段默认禁用)
            "FILTER_CONTROLS": {
                "ENABLE_ATR_FILTER": False,    # ATR波动率过滤器开关 (测试阶段禁用)
                "ENABLE_ADX_FILTER": False,    # ADX趋势强度过滤器开关 (测试阶段禁用)
                "ENABLE_BASIS_QUALITY_FILTER": False,  # 基差质量过滤器开关 (测试阶段禁用)
            },
            
            # 风险过滤器阈值 (测试阶段放宽)
            "VOLATILITY_FILTER": {
                "ATR_MULTIPLIER": 5.0,      # ATR波动率阈值倍数 (提高到5.0，放宽限制)
                "MIN_ATR_PERIODS": 14,      # 计算ATR的最小周期数
                "HIGH_VOLATILITY_THRESHOLD": 0.05,  # 高波动率阈值（5%，测试阶段放宽）
            },
            
            # 趋势强度过滤器 (测试阶段放宽)
            "TREND_FILTER": {
                "ADX_THRESHOLD": 5,         # ADX趋势强度阈值 (降低到5，便于测试)
                "ADX_PERIODS": 14,          # ADX计算周期
                "STRONG_TREND_THRESHOLD": 10,  # 强趋势阈值 (降低到10)
            },
            
            # 最小仓位比例（即使是B级信号也不能低于此比例）
            "MIN_POSITION_RATIO": 0.2,  # 20% - 最小仓位保护
            
            # 动态调整参数
            "DYNAMIC_ADJUSTMENT": {
                "VOLATILITY_WEIGHT": 0.4,   # 波动率权重
                "TREND_WEIGHT": 0.3,        # 趋势权重
                "BASIS_QUALITY_WEIGHT": 0.3, # 基差质量权重
            }
        }
    })
    
    # 交易对基本配置
    PAIR_CONFIG: Dict[str, Any] = field(default_factory=lambda: {
        "name": "DOGE_USDT",
        "spot_symbol": "DOGE/USDT",
        "futures_symbol": "DOGE/USDT:USDT",
        "min_notional": 5.0,
        "max_leverage": 10.0,
        "tick_size": 0.00001,
        "lot_size": 1.0,
        "max_holding_duration_hours": 24,
        "basis_focus_mode": True,
        "basis_convergence_target": 0.0005,
        "basis_profit_threshold": 0.001,
        "basis_stop_loss": 0.003,
        "basis_urgency_multiplier": 1.5,
    })
    
    # 系统配置  
    LOG_LEVEL: str = 'INFO'
    
    # 订单执行配置 - 三阶段智能执行策略
    ORDER_EXECUTION: Dict[str, Any] = field(default_factory=lambda: {
        # 基础超时配置
        "ORDER_TIMEOUT_SECONDS": 10,
        
        # 三阶段智能执行配置（沙盒环境优化版本）
        "SMART_EXECUTION": {
            # 阶段一：Post-Only Maker尝试
            "POST_ONLY_TIMEOUT_MS": 2000,   # 沙盒环境延长等待时间
            "MAKER_PRICE_OFFSET": 0.0,      # Maker价格偏移（0表示盘口价格）
            
            # 阶段二：IOC Taker执行
            "TAKER_PRICE_OFFSET": 0.0001,   # Taker价格偏移（穿越盘口）
            "IOC_TIMEOUT_MS": 1000,         # 沙盒环境延长确认时间
            
            # 阶段三：差额对冲
            "MIN_HEDGE_THRESHOLD": 0.01,   # 最小对冲阈值
            "HEDGE_PRICE_SLIPPAGE": 0.002, # 对冲滑点容忍
            "MAX_HEDGE_ATTEMPTS": 3,       # 最大对冲尝试次数
        },
        
        # 价格滑点容忍度
        "PRICE_SLIPPAGE_TOLERANCE": 0.001,  # 0.1%
        
        # 重试配置
        "MAX_RETRY_ATTEMPTS": 3,
        "RETRY_DELAY_SECONDS": 0.1,
        
        # 限价单价格调整（传统模式备用）
        "LIMIT_ORDER_PRICE_ADJUSTMENT": {
            "buy_adjustment": 1.001,
            "sell_adjustment": 0.999,
        },
        
        # 部分成交处理
        "PARTIAL_FILL_TIMEOUT": 3,
        "MIN_FILL_RATIO": 0.95,
        
        # 并发下单配置
        "CONCURRENT_ORDER_LIMIT": 2,
        "ORDER_BATCH_SIZE": 2,
        
        # 执行模式选择 - 调试优化
        "USE_SMART_EXECUTION": True,   # 启用智能执行进行调试
        "FALLBACK_TO_MARKET": True,    # 智能执行失败时是否回退到市价单
    })
    
    # 监控和告警配置
    MONITORING: Dict[str, Any] = field(default_factory=lambda: {
        # 系统健康检查间隔
        "HEALTH_CHECK_INTERVAL": 60,  # 秒
        
        # 性能统计间隔
        "STATS_REPORT_INTERVAL": 300,  # 5分钟
        
        # 告警阈值
        "ALERT_THRESHOLDS": {
            "high_latency_ms": 500,  # 降低延迟阈值
            "error_rate_percent": 5.0,
            "low_balance_ratio": 0.1,
        },
        
        # 操作和技术层面监控
        "HEARTBEAT_URL": None,
        "STALE_DATA_TIMEOUT_SECONDS": 10,  # 缩短数据超时阈值
        
        # 优化的日志轮转配置（配合Prometheus监控）
        "LOG_FILE_MAX_SIZE_MB": 50,  # 减小到50MB（Prometheus已监控高频数据）
        "LOG_FILE_BACKUP_COUNT": 3,  # 减少备份数量（由3个改为3个）
        "ERROR_LOG_MAX_SIZE_MB": 20,  # 错误日志单独配置
        "ERROR_LOG_BACKUP_COUNT": 5,  # 错误日志保留更多份
        "ENABLE_LOG_COMPRESSION": True,  # 启用压缩节省磁盘空间
        "LOG_ROTATION_WHEN": "midnight",  # 按时间轮转：每日午夜
        "LOG_ROTATION_INTERVAL": 1,  # 轮转间隔：1天
    })
    
    # 代理配置
    PROXY_CONFIG: Dict[str, Any] = field(default_factory=lambda: {
        "ENABLED": True,
        "URL": "socks5h://localhost:10809",
    })


# 创建全局配置实例
config = TradingConfig()


def validate_security_config():
    """验证安全配置 - 防止敏感信息泄露"""
    security_issues = []

    # 检查API密钥是否为空或默认值
    if not config.API_KEY or config.API_KEY == '':
        security_issues.append("API_KEY未设置，请配置环境变量")

    if not config.API_SECRET or config.API_SECRET == '':
        security_issues.append("API_SECRET未设置，请配置环境变量")

    if not config.API_PASSWORD or config.API_PASSWORD == '':
        security_issues.append("API_PASSWORD未设置，请配置环境变量")

    # 检查是否使用了示例密钥（防止意外使用）
    dangerous_patterns = [
        'your_api_key_here',
        'your_api_secret_here',
        'your_api_password_here',
        'example',
        'test',
        'demo',
        'placeholder'
    ]

    for pattern in dangerous_patterns:
        if pattern.lower() in config.API_KEY.lower():
            security_issues.append(f"API_KEY包含危险模式: {pattern}")
        if pattern.lower() in config.API_SECRET.lower():
            security_issues.append(f"API_SECRET包含危险模式: {pattern}")
        if pattern.lower() in config.API_PASSWORD.lower():
            security_issues.append(f"API_PASSWORD包含危险模式: {pattern}")

    # 检查生产环境配置
    if not config.USE_SANDBOX:
        warnings.warn("⚠️  当前配置为实盘模式，请确认所有参数正确！", UserWarning)
        if len(security_issues) > 0:
            raise ValueError("实盘模式下不允许存在安全配置问题")

    if security_issues:
        error_msg = "发现安全配置问题:\n" + "\n".join(f"- {issue}" for issue in security_issues)
        error_msg += "\n\n请检查环境变量配置或.env文件"
        raise ValueError(error_msg)

    return True


def get_exchange_config() -> Dict[str, Any]:
    """获取交易所连接配置"""
    return {
        'apiKey': config.API_KEY,
        'secret': config.API_SECRET,
        'password': config.API_PASSWORD,
        'sandbox': config.USE_SANDBOX,
        'enableRateLimit': True,
        'timeout': 30000,
        'options': {
            'adjustForTimeDifference': True,
            'recvWindow': 10000,
            'brokerId': 'CCXT',
            'defaultType': 'spot',
            'marginMode': 'cross',
            'positionMode': 'net_mode',
        },
        'headers': {
            'Content-Type': 'application/json',
            'User-Agent': 'AsyncArbitrageBot'
        }
    }


def get_risk_params() -> Dict[str, Any]:
    """获取风险管理参数"""
    return config.RISK_MANAGEMENT.copy()


def calculate_position_size(current_price: float, pair_name: str, risk_ratio: float = 0.25) -> float:
    """计算基于风控的仓位大小 - 多交易对版本"""
    risk_params = get_risk_params()
    pair_config = get_pair_config(pair_name)
    
    if not pair_config:
        raise ValueError(f"未找到交易对配置: {pair_name}")
    
    # 获取该交易对的最大资金分配
    max_capital_per_pair = (
        risk_params["TOTAL_CAPITAL_ALLOCATION"] * 
        risk_params["MAX_CAPITAL_PER_PAIR_RATIO"]
    )
    
    max_capital_per_trade = (
        risk_params["TOTAL_CAPITAL_ALLOCATION"] * 
        risk_params["MAX_CAPITAL_PER_TRADE_RATIO"]
    )
    
    # 取较小值作为最终限制
    effective_capital = min(max_capital_per_pair, max_capital_per_trade)
    max_position_value = effective_capital * risk_ratio
    position_size = max_position_value / current_price
    
    # 确保符合最小交易单位
    lot_size = pair_config["lot_size"]
    position_size = (position_size // lot_size) * lot_size
    
    return position_size


def get_enabled_trading_pairs() -> List[Dict[str, Any]]:
    """获取启用的交易对列表"""
    return [pair for pair in config.TRADING_PAIRS if pair.get("enabled", True)]


def get_pair_config(pair_name: str) -> Optional[Dict[str, Any]]:
    """根据交易对名称获取配置"""
    for pair in config.TRADING_PAIRS:
        if pair["name"] == pair_name:
            return pair
    return None


def get_priority_pairs() -> List[Dict[str, Any]]:
    """获取按优先级排序的交易对列表"""
    enabled_pairs = get_enabled_trading_pairs()
    return sorted(enabled_pairs, key=lambda x: x.get("priority", 999))


def get_all_symbols() -> Tuple[List[str], List[str]]:
    """获取所有现货和期货交易对符号"""
    enabled_pairs = get_enabled_trading_pairs()
    spot_symbols = [pair["spot_id"] for pair in enabled_pairs]
    futures_symbols = [pair["futures_id"] for pair in enabled_pairs]
    return spot_symbols, futures_symbols


def set_filter_controls(enable_atr: bool = True, enable_adx: bool = True, enable_basis_quality: bool = True):
    """
    动态设置风险过滤器开关
    
    Args:
        enable_atr: 是否启用ATR波动率过滤器
        enable_adx: 是否启用ADX趋势强度过滤器  
        enable_basis_quality: 是否启用基差质量过滤器
    """
    filter_controls = config.RISK_MANAGEMENT["GRADED_POSITION_SYSTEM"]["FILTER_CONTROLS"]
    filter_controls["ENABLE_ATR_FILTER"] = enable_atr
    filter_controls["ENABLE_ADX_FILTER"] = enable_adx
    filter_controls["ENABLE_BASIS_QUALITY_FILTER"] = enable_basis_quality
    
    status = []
    if enable_atr:
        status.append("ATR✅")
    else:
        status.append("ATR❌")
    
    if enable_adx:
        status.append("ADX✅")
    else:
        status.append("ADX❌") 
        
    if enable_basis_quality:
        status.append("BASIS✅")
    else:
        status.append("BASIS❌")
    
    print(f"🎛️  风险过滤器设置更新: {' | '.join(status)}")


def disable_all_filters():
    """禁用所有风险过滤器 - 测试模式"""
    set_filter_controls(enable_atr=False, enable_adx=False, enable_basis_quality=False)
    print("⚠️  已禁用所有风险过滤器 - 测试模式")


def enable_all_filters():
    """启用所有风险过滤器 - 生产模式"""
    set_filter_controls(enable_atr=True, enable_adx=True, enable_basis_quality=True)
    print("🛡️  已启用所有风险过滤器 - 生产模式")


def calibrate_safety_margin(target_margin: float = 1.5):
    """
    校准利润安全边际

    Args:
        target_margin: 目标安全边际倍数（建议1.5-2.0）
    """
    if target_margin < 1.0:
        raise ValueError("安全边际不能小于1.0")

    config.TRADING_COSTS["PROFIT_SAFETY_MARGIN"] = target_margin
    print(f"📊 利润安全边际已校准为: {target_margin}x ({(target_margin-1)*100:.0f}%缓冲)")


def calibrate_holding_time(hours: float):
    """
    校准预期持仓时间

    Args:
        hours: 预期持仓小时数（基于历史回测数据）
    """
    if hours <= 0:
        raise ValueError("持仓时间必须大于0")

    config.TRADING_COSTS["EXPECTED_HOLDING_HOURS"] = hours

    # 转换为更直观的显示
    if hours < 1:
        minutes = hours * 60
        print(f"⏱️  预期持仓时间已校准为: {minutes:.0f}分钟")
    else:
        print(f"⏱️  预期持仓时间已校准为: {hours:.2f}小时")


def set_production_parameters():
    """设置生产级参数配置"""
    print("🚀 切换到生产级参数配置...")

    # 校准安全边际
    calibrate_safety_margin(1.5)

    # 校准持仓时间（基差套利通常15-30分钟）
    calibrate_holding_time(0.25)

    # 启用所有风险过滤器
    enable_all_filters()

    # 调整布林带参数为更保守的设置
    config.BOLLINGER_STD_DEV = 2.0  # 提高到2.0标准差
    config.BASIS_ENTRY_THRESHOLD = 0.001  # 提高到0.1%

    print("✅ 生产级参数配置完成")


def set_test_parameters():
    """设置测试级参数配置"""
    print("🧪 切换到测试级参数配置...")

    # 降低安全边际便于测试
    calibrate_safety_margin(1.2)

    # 保持较短的持仓时间预期
    calibrate_holding_time(0.25)

    # 禁用风险过滤器便于测试
    disable_all_filters()

    # 调整布林带参数为更敏感的设置
    config.BOLLINGER_STD_DEV = 1.5  # 降低到1.5标准差
    config.BASIS_ENTRY_THRESHOLD = 0.0005  # 降低到0.05%

    print("✅ 测试级参数配置完成")


def get_current_parameters_summary() -> Dict[str, Any]:
    """获取当前参数配置摘要"""
    return {
        "trading_costs": {
            "profit_safety_margin": config.TRADING_COSTS["PROFIT_SAFETY_MARGIN"],
            "expected_holding_hours": config.TRADING_COSTS["EXPECTED_HOLDING_HOURS"],
            "taker_fee_rate": config.TRADING_COSTS["TAKER_FEE_RATE"],
        },
        "strategy_params": {
            "bollinger_window": config.BOLLINGER_WINDOW,
            "bollinger_std_dev": config.BOLLINGER_STD_DEV,
            "basis_entry_threshold": config.BASIS_ENTRY_THRESHOLD,
            "basis_profit_target": config.BASIS_PROFIT_TARGET,
            "basis_stop_loss": config.BASIS_STOP_LOSS,
        },
        "layered_bollinger": config.STRATEGY_OPTIMIZATION.get("LAYERED_BOLLINGER_BANDS", {}),
        "filter_status": get_filter_status(),
        "data_preprocessing": {
            "vwap_enabled": config.DATA_PREPROCESSING["VWAP_AGGREGATION"]["VOLUME_WEIGHTED"],
            "quality_control_enabled": config.DATA_PREPROCESSING["QUALITY_CONTROL"]["ENABLE_OUTLIER_DETECTION"],
            "smoothing_enabled": config.DATA_PREPROCESSING["SMOOTHING"]["ENABLE_PRICE_SMOOTHING"],
        }
    }


def print_parameters_summary():
    """打印当前参数配置摘要"""
    summary = get_current_parameters_summary()

    print("\n" + "="*60)
    print("📋 当前策略参数配置摘要")
    print("="*60)

    print(f"\n💰 交易成本配置:")
    print(f"  • 利润安全边际: {summary['trading_costs']['profit_safety_margin']:.2f}x")
    print(f"  • 预期持仓时间: {summary['trading_costs']['expected_holding_hours']:.2f}小时")
    print(f"  • 手续费率: {summary['trading_costs']['taker_fee_rate']:.3f}%")

    print(f"\n📊 策略参数:")
    print(f"  • 布林带窗口: {summary['strategy_params']['bollinger_window']}")
    print(f"  • 布林带标准差: {summary['strategy_params']['bollinger_std_dev']}")
    print(f"  • 基差入场阈值: {summary['strategy_params']['basis_entry_threshold']:.4f}")

    print(f"\n🎯 分层布林带:")
    layered = summary['layered_bollinger']
    if layered:
        strategic = layered.get('STRATEGIC_BB', {})
        tactical = layered.get('TACTICAL_BB', {})
        print(f"  • 战略层: {strategic.get('WINDOW', 'N/A')}分钟, {strategic.get('STD_DEV', 'N/A')}σ")
        print(f"  • 战术层: {tactical.get('WINDOW', 'N/A')}秒, {tactical.get('STD_DEV', 'N/A')}σ")
    else:
        print("  • 未配置")

    print(f"\n🛡️  风险过滤器:")
    filters = summary['filter_status']
    print(f"  • ATR过滤器: {'✅' if filters['ATR_FILTER'] else '❌'}")
    print(f"  • ADX过滤器: {'✅' if filters['ADX_FILTER'] else '❌'}")
    print(f"  • 基差质量过滤器: {'✅' if filters['BASIS_QUALITY_FILTER'] else '❌'}")

    print(f"\n🔧 数据预处理:")
    preprocessing = summary['data_preprocessing']
    print(f"  • VWAP聚合: {'✅' if preprocessing['vwap_enabled'] else '❌'}")
    print(f"  • 质量控制: {'✅' if preprocessing['quality_control_enabled'] else '❌'}")
    print(f"  • 价格平滑: {'✅' if preprocessing['smoothing_enabled'] else '❌'}")

    print("="*60 + "\n")


def get_filter_status() -> Dict[str, bool]:
    """获取当前过滤器状态"""
    filter_controls = config.RISK_MANAGEMENT["GRADED_POSITION_SYSTEM"]["FILTER_CONTROLS"]
    return {
        "ATR_FILTER": filter_controls.get("ENABLE_ATR_FILTER", True),
        "ADX_FILTER": filter_controls.get("ENABLE_ADX_FILTER", True),
        "BASIS_QUALITY_FILTER": filter_controls.get("ENABLE_BASIS_QUALITY_FILTER", True)
    }


def validate_config() -> bool:
    """
    验证配置参数的有效性 - 多交易对版本

    Returns:
        bool: 验证是否通过

    Raises:
        ValueError: 配置验证失败时抛出
    """
    if not config.API_KEY or not config.API_SECRET:
        raise ValueError("API密钥和密码不能为空")
    
    if config.BOLLINGER_WINDOW <= 1:
        raise ValueError("布林带窗口必须大于1")
    
    if config.BOLLINGER_STD_DEV <= 0:
        raise ValueError("布林带标准差必须大于0")
    
    if config.BASIS_ENTRY_THRESHOLD <= 0:
        raise ValueError("基差入场阈值必须大于0")
    
    # 验证多交易对配置
    enabled_pairs = get_enabled_trading_pairs()
    if not enabled_pairs:
        raise ValueError("至少需要启用一个交易对")
    
    if len(enabled_pairs) > config.RISK_MANAGEMENT.get("MAX_CONCURRENT_PAIRS", 3):
        raise ValueError(f"启用的交易对数量({len(enabled_pairs)})超过最大限制")
    
    # 验证每个交易对配置
    pair_names = set()
    for i, pair in enumerate(enabled_pairs):
        if not pair.get("name"):
            raise ValueError(f"交易对{i}缺少name字段")
        
        if pair["name"] in pair_names:
            raise ValueError(f"交易对名称重复: {pair['name']}")
        pair_names.add(pair["name"])
        
        required_fields = ["spot_id", "futures_id", "min_notional", "lot_size"]
        for field in required_fields:
            if field not in pair:
                raise ValueError(f"交易对{pair['name']}缺少必需字段: {field}")
        
        if pair.get("priority", 1) < 1:
            raise ValueError(f"交易对{pair['name']}优先级必须>=1")
    
    # 验证分级仓位系统参数
    graded_system = config.RISK_MANAGEMENT.get("GRADED_POSITION_SYSTEM", {})
    
    # 验证次优信号仓位比例
    suboptimal_ratio = graded_system.get("SUBOPTIMAL_TRADE_SIZE_RATIO", 0.5)
    if not (0.0 <= suboptimal_ratio <= 1.0):
        raise ValueError("SUBOPTIMAL_TRADE_SIZE_RATIO必须在0.0和1.0之间")
    
    # 验证最小仓位比例
    min_position_ratio = graded_system.get("MIN_POSITION_RATIO", 0.2)
    if not (0.0 <= min_position_ratio <= 1.0):
        raise ValueError("MIN_POSITION_RATIO必须在0.0和1.0之间")
    
    # 验证波动率过滤器参数
    volatility_filter = graded_system.get("VOLATILITY_FILTER", {})
    atr_multiplier = volatility_filter.get("ATR_MULTIPLIER", 2.0)
    if atr_multiplier <= 0:
        raise ValueError("ATR_MULTIPLIER必须大于0")
    
    min_atr_periods = volatility_filter.get("MIN_ATR_PERIODS", 14)
    if min_atr_periods < 1:
        raise ValueError("MIN_ATR_PERIODS必须至少为1")
    
    # 验证趋势过滤器参数
    trend_filter = graded_system.get("TREND_FILTER", {})
    adx_periods = trend_filter.get("ADX_PERIODS", 14)
    if adx_periods < 1:
        raise ValueError("ADX_PERIODS必须至少为1")
    
    # 验证动态调整权重总和
    dynamic_adj = graded_system.get("DYNAMIC_ADJUSTMENT", {})
    weights = [
        dynamic_adj.get("VOLATILITY_WEIGHT", 0.4),
        dynamic_adj.get("TREND_WEIGHT", 0.3),
        dynamic_adj.get("BASIS_QUALITY_WEIGHT", 0.3)
    ]
    
    if any(w < 0 or w > 1 for w in weights):
        raise ValueError("动态调整权重必须在0和1之间")
    
    weight_sum = sum(weights)
    if abs(weight_sum - 1.0) > 0.01:  # 允许小误差
        raise ValueError(f"动态调整权重总和必须为1.0，当前为{weight_sum:.3f}")
    
    # 验证次优比例不小于最小比例
    if suboptimal_ratio < min_position_ratio:
        raise ValueError(
            f"SUBOPTIMAL_TRADE_SIZE_RATIO ({suboptimal_ratio}) "
            f"不能小于MIN_POSITION_RATIO ({min_position_ratio})"
        )

    print("✅ 配置验证通过")
    return True


# 日志配置 - 结构化JSON日志系统
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S.%f'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] [%(process)d:%(thread)d] %(name)s:%(lineno)d - %(funcName)s() - %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S.%f'
        },
        'json': {
            '()': 'log_utils.JsonFormatter'
        },
    },
    'handlers': {
        'console': {
            'level': config.LOG_LEVEL,
            'formatter': 'standard',
            'class': 'logging.StreamHandler',
        },
        'file': {
            'level': config.LOG_LEVEL,
            'formatter': 'detailed',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'arbitrage_bot.log',
            'maxBytes': config.MONITORING["LOG_FILE_MAX_SIZE_MB"] * 1024 * 1024,
            'backupCount': config.MONITORING["LOG_FILE_BACKUP_COUNT"],
            'mode': 'a',
            'encoding': 'utf-8',
        },
        'json_file': {
            'level': config.LOG_LEVEL,
            'formatter': 'json',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'arbitrage_bot_structured.log',
            'maxBytes': config.MONITORING["LOG_FILE_MAX_SIZE_MB"] * 1024 * 1024,
            'backupCount': config.MONITORING["LOG_FILE_BACKUP_COUNT"],
            'mode': 'a',
            'encoding': 'utf-8',
        },
        'error_file': {
            'level': 'ERROR',
            'formatter': 'detailed',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'arbitrage_bot_errors.log',
            'maxBytes': config.MONITORING["ERROR_LOG_MAX_SIZE_MB"] * 1024 * 1024,
            'backupCount': config.MONITORING["ERROR_LOG_BACKUP_COUNT"],
            'mode': 'a',
            'encoding': 'utf-8',
        },
        'json_error_file': {
            'level': 'ERROR',
            'formatter': 'json',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'arbitrage_bot_structured_errors.log',
            'maxBytes': config.MONITORING["ERROR_LOG_MAX_SIZE_MB"] * 1024 * 1024,
            'backupCount': config.MONITORING["ERROR_LOG_BACKUP_COUNT"],
            'mode': 'a',
            'encoding': 'utf-8',
        },
        # 时间轮转日志（每日）- 长期归档
        'daily_archive': {
            'level': 'INFO',
            'formatter': 'standard',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': 'arbitrage_bot_daily.log',
            'when': config.MONITORING["LOG_ROTATION_WHEN"],
            'interval': config.MONITORING["LOG_ROTATION_INTERVAL"],
            'backupCount': 7,  # 保留7天的日志
            'encoding': 'utf-8',
            'utc': False,
        },
    },
    'loggers': {
        '': {
            'handlers': ['console', 'file', 'json_file', 'error_file', 'json_error_file', 'daily_archive'],
            'level': config.LOG_LEVEL,
            'propagate': False
        },
        'arbitrage.orders': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False
        },
        'arbitrage.positions': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False
        },
        'arbitrage.websocket': {
            'handlers': ['file'],
            'level': 'DEBUG',
            'propagate': False
        },
    }
}